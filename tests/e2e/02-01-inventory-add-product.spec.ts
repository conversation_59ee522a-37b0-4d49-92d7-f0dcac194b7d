import { test, expect, Request } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure');
  }
});

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
});

test.use({ storageState: CONST.authFile })

const requestTimings = new Map<Request, number>();

test('Add Product', async ({ page }) => {
  test.setTimeout(680000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page.click('button#add-product-dropdown-button')
  await page.click('button#add-single-product-button')

  page.on('request', (request) => {
    requestTimings.set(request, Date.now());
  });

  page.on('console', msg => {
    console.log(`[Console ${msg.type()}] ${msg.text()}`);
  });

  page.on('response', async (response) => {
    console.log('Response URL:', response.url());
    console.log('Response Status:', response.status());

    const request = response.request();
    const startTime = requestTimings.get(request);
    if (startTime) {
      const latency = Date.now() - startTime;
      console.log(`Response Time: ${latency}ms`);
      requestTimings.delete(request);
    }

    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json();
        console.log('GraphQL Response:', JSON.stringify(responseBody));
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page.screenshot({
    path: 'tests/screenshots/add_product_page_loading.png',
  })

  await page.waitForSelector('text=Product Info', { timeout: 25000 });

  await page.screenshot({
    path: 'tests/screenshots/add_product_page_loading_1.png',
  })

  //Product Info
  await page
    .locator('input#productName')
    .pressSequentially(CONST.testProduct.productName, { delay: 500 })


  // await page.click('button:has-text("Use Copilot")')
  await page
    .locator('input#category')
    .pressSequentially(
      CONST.testProduct.category,
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.category}")`
  )

  await page
    .locator('input#factoryLocation')
    .pressSequentially(
      CONST.testProduct.mapboxAutocompleteInputs[0].searchKeyword,
      { delay: 100 }
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.mapboxAutocompleteInputs[0].searchResult}")`
  )

  await page
    .locator('input#targetMarketLocation')
    .pressSequentially(
      CONST.testProduct.mapboxAutocompleteInputs[1].searchKeyword,
      { delay: 100 }
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.mapboxAutocompleteInputs[1].searchResult}")`
  )

  await page.getByRole('button', { name: 'right Optional Data' }).click();

  await page.getByPlaceholder('1.5L').fill('1L');

  await page.click('button:has-text("Next")')

  //Ingredients
  //Ingredient 1
  await page.click('button#add-ingredient-button')

  await page.fill(
    'form[id="add-ingredient-form"] input#ingredient',
    CONST.testProduct.ingredients[0].name
  )
  await page.fill(
    'form[id="add-ingredient-form"] input#weight',
    CONST.testProduct.ingredients[0].weight
  )

  await page.click('button#add-ingredient-submit-button')

  await page.waitForTimeout(1000)

  //Ingredient 2
  await page.click('button#add-ingredient-button')

  await page.fill(
    'form[id="add-ingredient-form"] input#ingredient',
    CONST.testProduct.ingredients[1].name
  )
  await page.fill(
    'form[id="add-ingredient-form"] input#weight',
    CONST.testProduct.ingredients[1].weight
  )

  await page.click('input#advanced-ingredient-fields-checkbox')

  await page.fill(
    'form[id="add-ingredient-form"] input#supplierName',
    CONST.testProduct.ingredients[1].supplierName ?? ''
  )
  await page
    .locator('form[id="add-ingredient-form"] input#supplierOrigin')
    .pressSequentially(
      CONST.testProduct.ingredients[1].mapboxAutocompleteInputs
        ?.searchKeyword ?? '',
      { delay: 100 }
    )

  await page.waitForSelector(
    '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'
  )

  await page.click(
    `.ant-select-dropdown .ant-select-item-option:has-text("${CONST.testProduct.ingredients[1].mapboxAutocompleteInputs?.searchResult}")`
  )

  await page.click('button#add-ingredient-submit-button')

  await page.waitForTimeout(1000)

  //Ingredient 3
  await page.click('button#add-ingredient-button')

  await page.fill(
    'form[id="add-ingredient-form"] input#ingredient',
    CONST.testProduct.ingredients[2].name
  )
  await page.fill(
    'form[id="add-ingredient-form"] input#weight',
    CONST.testProduct.ingredients[2].weight
  )

  await page.click('button#add-ingredient-submit-button')

  await page.waitForTimeout(1000)

  //Ingredient Source Prediction
  const ingredient1SourceSelector =
    '#add-ingredient-table table tbody tr:nth-child(2) td:nth-child(5)'
  await page.screenshot({
    path: 'tests/screenshots/before_waiting_for_source_ingredient1.png',
  })
  // Wait until the source country is predicted
  await page.waitForFunction(
    (ingredient1SourceSelector) => {
      const cell = document.querySelector(ingredient1SourceSelector)
      return cell && cell.textContent?.trim().includes('AI')
    },
    ingredient1SourceSelector,
    { timeout: 80000 }
  )
  await page.screenshot({
    path: 'tests/screenshots/after_waiting_for_source_ingredient1.png',
  })

  const ingredient2SourceSelector =
    '#add-ingredient-table table tbody tr:nth-child(4) td:nth-child(5)'
  await page.screenshot({
    path: 'tests/screenshots/before_waiting_for_source_ingredient2.png',
  })
  // Wait until the source country is predicted
  await page.waitForFunction(
    (ingredient2SourceSelector) => {
      const cell = document.querySelector(ingredient2SourceSelector)
      return cell && cell.textContent?.trim().includes('AI')
    },
    ingredient2SourceSelector,
    { timeout: 80000 }
  )
  await page.screenshot({
    path: 'tests/screenshots/after_waiting_for_source_ingredient2.png',
  })

  //Edit Ingredient
  await page.getByRole('button', { name: 'Edit', exact: true }).nth(1).click();
  await page.getByPlaceholder('120').click();
  await page.getByPlaceholder('120').fill('52.25');
  await page.getByRole('button', { name: 'Add', exact: true }).click();
  await page.waitForSelector('text=52.25 g');

  //Delete Ingredient
  await page.getByRole('button', { name: 'Delete' }).nth(1).click();
  await page.getByRole('button', { name: 'OK' }).click();

  for (const rawMaterial of CONST.testProduct.ingredients.slice(3)) {
    await page.waitForTimeout(1000);
    await page.getByRole('button', { name: 'plus-circle Add Raw Material' }).click();
    await page.getByLabel('Raw Material Name').click();
    await page.getByLabel('Raw Material Name').fill(rawMaterial.name);
    await page.getByTitle(rawMaterial.name, { exact: true }).locator('div').click();
    await page.getByPlaceholder('120').click();
    await page.getByPlaceholder('120').fill(rawMaterial.weight);
    await page.getByRole('button', { name: 'Add', exact: true }).click();
  }

  let ingredientCount = await page.$$eval(
    'div#add-ingredient-table table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(ingredientCount).toBe(CONST.testProduct.ingredients.length - 1);

  //wait for loading spinner to disappear
  await page.waitForFunction(
    () =>
      !document
        .querySelector('button#next-button')
        ?.classList.contains('ant-btn-loading')
  )

  await page.getByRole('button', { name: 'edit', exact: true }).first().click();

  console.log('=== EMISSIONS FACTOR SEARCH DEBUG START ===');

  // Log initial state before search
  console.log('1. About to fill search field with "citric acid"');
  await page.getByPlaceholder('Name').fill('citric acid');

  console.log('2. Search field filled, about to click search button');

  // Add comprehensive request/response logging
  page.on('request', (request) => {
    if (request.url().includes('/raw-materials/activities/recommendations')) {
      console.log('🚀 EMISSIONS FACTOR API REQUEST INITIATED:');
      console.log('   URL:', request.url());
      console.log('   Method:', request.method());
      console.log('   Headers:', request.headers());
      try {
        const postData = request.postData();
        if (postData) {
          console.log('   Post Data:', postData);
        }
      } catch (error) {
        console.error('   Error reading request data:', error);
      }
    }
  });

  page.on('response', async (response) => {
    if (response.url().includes('/raw-materials/activities/recommendations')) {
      console.log('3. EMISSIONS FACTOR API RESPONSE RECEIVED:');
      console.log('   URL:', response.url());
      console.log('   Status:', response.status());
      try {
        const responseBody = await response.json();
        console.log('   Response Body:', JSON.stringify(responseBody, null, 2));

        // Check if trichloroacetic acid is in the response
        const hasTriacid = JSON.stringify(responseBody).toLowerCase().includes('trichloroacetic acid');
        console.log('   Contains "trichloroacetic acid":', hasTriacid);

        // Log all activity names in the response
        if (responseBody.matched_activity) {
          console.log('   Matched Activity:', responseBody.matched_activity.activity_name);
        }
        if (responseBody.recommendations) {
          console.log('   Recommendations:');
          responseBody.recommendations.forEach((rec: any, index: number) => {
            console.log(`     ${index + 1}. ${rec.activity_name}`);
          });
        }
      } catch (error) {
        console.error('   Error parsing emissions factor response:', error);
      }
    }
  });

  // Add console error logging
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log(`[Browser Console Error] ${msg.text()}`);
    }
  });

  // Add page error logging
  page.on('pageerror', error => {
    console.log(`[Page Error] ${error.message}`);
  });

  // Check if the search button exists and is enabled
  console.log('2.1. Checking search button state...');
  const searchButton = page.locator('button#search-ef-button');
  const buttonExists = await searchButton.count();
  console.log('   Search button exists:', buttonExists > 0);

  if (buttonExists > 0) {
    const isEnabled = await searchButton.isEnabled();
    const isVisible = await searchButton.isVisible();
    console.log('   Search button enabled:', isEnabled);
    console.log('   Search button visible:', isVisible);

    // Get button text
    const buttonText = await searchButton.textContent();
    console.log('   Search button text:', buttonText);
  }

  // Check form state before clicking
  console.log('2.2. Checking form state...');
  const nameInput = page.getByPlaceholder('Name');
  const nameValue = await nameInput.inputValue();
  console.log('   Name input value:', nameValue);

  await page.click('button#search-ef-button');
  console.log('4. Search button clicked, waiting for API response...');

  // Wait a moment to see if any immediate errors occur
  await page.waitForTimeout(1000);
  console.log('4.1. Waited 1 second after button click...');

  // Check if button is in loading state
  console.log('4.2. Checking if button is in loading state...');
  const buttonHasLoadingClass = await searchButton.evaluate(el => el.classList.contains('ant-btn-loading'));
  console.log('   Button has loading class:', buttonHasLoadingClass);

  // Check if there's a loading spinner in the table
  console.log('4.3. Checking for loading indicators...');
  const hasTableLoading = await page.locator('.ant-spin-spinning').count();
  console.log('   Table loading spinners found:', hasTableLoading);

  // Check network activity
  console.log('4.4. Checking for any GraphQL requests...');

  // Set up a promise to track if we get the specific API call
  const apiCallPromise = new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('No emissions factor API call detected within 10 seconds'));
    }, 10000);

    page.on('request', (request) => {
      if (request.url().includes('/raw-materials/activities/recommendations')) {
        clearTimeout(timeout);
        resolve(request);
      }
    });
  });

  try {
    console.log('4.5. Waiting up to 10 seconds for emissions factor API call...');
    await apiCallPromise;
    console.log('✅ Emissions factor API call detected!');

    // Now wait for the response
    await page.waitForResponse(response =>
      response.url().includes('/raw-materials/activities/recommendations') && response.status() === 200,
      { timeout: 30000 }
    );
    console.log('5. API response received, waiting for table to update...');

  } catch (error) {
    console.log('❌ No emissions factor API call detected!');
    console.log('   Error:', error.message);

    // Let's check what GraphQL calls ARE happening
    console.log('4.6. Checking what GraphQL calls are happening...');
    const allRequests: Array<{url: string, method: string, postData: string | null}> = [];

    // Capture all requests for the next 5 seconds
    const requestCapture = new Promise<Array<{url: string, method: string, postData: string | null}>>((resolve) => {
      setTimeout(() => {
        resolve(allRequests);
      }, 5000);

      page.on('request', (request) => {
        if (request.url().includes('graphql') || request.url().includes('recommendations')) {
          allRequests.push({
            url: request.url(),
            method: request.method(),
            postData: request.postData()
          });
        }
      });
    });

    const requests = await requestCapture;
    console.log('   GraphQL requests in last 5 seconds:', requests.length);
    requests.forEach((req: any, index: number) => {
      console.log(`   Request ${index + 1}:`, req.url);
      if (req.postData) {
        console.log(`     Post Data:`, req.postData.substring(0, 200) + '...');
      }
    });

    // Check if there are any JavaScript errors
    console.log('4.7. Checking for JavaScript errors...');
    const errors = await page.evaluate(() => {
      return window.console.error.toString();
    });
    console.log('   Console errors:', errors);

    // Check component state
    console.log('4.8. Checking EmissionsFactorSelector component state...');
    const componentState = await page.evaluate(() => {
      // Try to find the component and check its state
      const drawer = document.querySelector('.ant-drawer');
      const form = document.querySelector('form');
      const searchButton = document.querySelector('button#search-ef-button') as HTMLButtonElement;

      return {
        drawerExists: !!drawer,
        drawerVisible: drawer ? !drawer.classList.contains('ant-drawer-hidden') : false,
        formExists: !!form,
        searchButtonExists: !!searchButton,
        searchButtonDisabled: searchButton ? searchButton.disabled : null,
        searchButtonText: searchButton ? searchButton.textContent : null
      };
    });
    console.log('   Component state:', componentState);

    // Since the API call didn't happen, let's continue with a modified approach
    console.log('4.9. API call failed to trigger, but continuing test to see current state...');
  }

  // Wait a bit for the UI to update
  await page.waitForTimeout(2000);

  // Log current table contents
  console.log('6. Checking current table contents...');
  try {
    const tableRows = await page.$$eval('table tbody tr', rows =>
      rows.map(row => ({
        text: row.textContent?.trim(),
        visible: (row as HTMLElement).offsetHeight > 0
      }))
    );
    console.log('   Table rows found:', tableRows.length);
    tableRows.forEach((row, index) => {
      console.log(`   Row ${index + 1}: "${row.text}" (visible: ${row.visible})`);
    });

    // Check specifically for trichloroacetic acid
    const hasTriacidRow = tableRows.some(row =>
      row.text?.toLowerCase().includes('trichloroacetic acid')
    );
    console.log('   Table contains "trichloroacetic acid" row:', hasTriacidRow);

  } catch (error) {
    console.error('   Error reading table contents:', error);
  }

  // Try to find the specific row
  console.log('7. Attempting to find trichloroacetic acid row...');
  try {
    const triAcidRow = page.getByRole('row', { name: 'trichloroacetic acid' }).first();
    const isVisible = await triAcidRow.isVisible({ timeout: 5000 });
    console.log('   Trichloroacetic acid row visible:', isVisible);

    if (isVisible) {
      console.log('8. Row found! Attempting to check the checkbox...');
      await triAcidRow.getByLabel('', { exact: true }).check();
      console.log('9. Checkbox checked successfully');
    } else {
      console.log('8. Row not visible, listing all available rows with "acid" in name...');
      const acidRows = await page.$$eval('table tbody tr', rows =>
        rows.filter(row => row.textContent?.toLowerCase().includes('acid'))
             .map(row => row.textContent?.trim())
      );
      console.log('   Rows containing "acid":', acidRows);
    }
  } catch (error) {
    console.error('   Error finding trichloroacetic acid row:', error);

    // Fallback: try to find any row with "acid" and log details
    console.log('   Fallback: Looking for any rows with "acid"...');
    try {
      const allRows = await page.$$eval('table tbody tr', rows =>
        rows.map(row => row.textContent?.trim())
      );
      const acidRows = allRows.filter(row => row?.toLowerCase().includes('acid'));
      console.log('   All rows with "acid":', acidRows);
    } catch (fallbackError) {
      console.error('   Fallback also failed:', fallbackError);
    }
  }

  console.log('=== EMISSIONS FACTOR SEARCH DEBUG END ===');

  await page.getByRole('row', { name: 'trichloroacetic acid' }).first().getByLabel('', { exact: true }).check();
  await page.getByRole('button', { name: 'Update' }).click();
  await page.waitForSelector('text=trichloroacetic acid');

  //Packaging
  //wait for packaging predictions
  await page.click('button:has-text("Use Copilot")')

  await page.waitForSelector('text=PET', { timeout: 230000, state: 'visible' })

  const packagingCount = await page.$$eval(
    'div#add-packaging-table table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  //Edit Packaging
  await page.locator('#add-packaging-table')
    .locator('tr', { hasText: 'PET' })
    .getByRole('cell', { name: 'Edit Delete' })
    .getByRole('button', { name: 'Edit' })
    .click();

  await page.getByPlaceholder('90').click();
  await page.getByPlaceholder('90').fill('14.717');
  await page.getByRole('button', { name: 'Add', exact: true }).click();
  await page.waitForSelector('text=14.72 g');

  //Delete Packaging
  await page.locator('#add-packaging-table')
    .locator('tr', { hasText: 'PET' })
    .getByRole('cell', { name: 'Edit Delete' })
    .getByRole('button', { name: 'Delete' })
    .click();

  await page.getByRole('button', { name: 'OK' }).click();


  for (const packaging of CONST.testProduct.packaging) {
    await page.waitForTimeout(1000);
    await page.getByRole('button', { name: 'plus-circle Add Packaging' }).click();
    await page.getByLabel('Packaging Material').click();
    await page.getByLabel('Packaging Material').fill(packaging.material);
    await page.getByTitle(packaging.material).locator('div').click();
    await page.click('input#packaging-component');
    await page.locator('input#packaging-component').fill(packaging.component);
    await page.getByTitle(packaging.component).locator('div').click();
    await page.getByPlaceholder('90').fill(packaging.weight);
    await page.getByRole('button', { name: 'Add', exact: true }).click();
  }

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Predicted', { timeout: 220000 });

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Factory to Retail', { timeout: 50000 });

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Consumer use', { timeout: 10000 });

  const consumerUseRowSelector =
  '#add-consumer-use-table table tbody tr:nth-child(2) td:nth-child(2)'
  // Wait until consumer use is predicted
  await page.waitForFunction(
    (consumerUseRowSelector) => {
      const cell = document.querySelector(consumerUseRowSelector)
      return cell && cell.textContent?.trim().includes('AI')
    },
    consumerUseRowSelector,
    { timeout: 120000 }
  )

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=End of life', { timeout: 1000 });

  await page.click('button:has-text("Next")')

  //wait for loading spinner to disappear
  await page.waitForFunction(
    () =>
      !document
        .querySelector('button#finish-button')
        ?.classList.contains('ant-btn-loading')
  )

  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(15000);

  //Submit
  await page.click('button:has-text("Save")')

  await page.context().storageState({ path: CONST.authFile })

  await page.waitForSelector(
    `text=Product ${CONST.testProduct.productName} created successfully`,
    { timeout: 520000 }
  )

  await page.context().storageState({ path: CONST.authFile })

  await page.screenshot({
    path: 'tests/screenshots/add_product_create_product.png',
  })

  await page.waitForURL(`${CONST.baseURL}/products`)

  await page.screenshot({
    path: 'tests/screenshots/add_product_inventory_redirect.png',
  })
})
