[Console error] Warning: Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.%s

Check the render method of `Body`.
    at ImmutableComponent2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:53086:7)
    at Body (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:53747:5)
    at ImmutableComponent2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:53086:7)
    at table
    at div
    at div
    at div
    at DomWrapper2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-UEK5KTZZ.js?v=9f539c35:898:5)
    at SingleObserver (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-UEK5KTZZ.js?v=9f539c35:940:24)
    at ResizeObserver2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-UEK5KTZZ.js?v=9f539c35:1015:24)
    at Provider2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:52979:22)
    at Table (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:54744:31)
    at ImmutableComponent2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:53058:37)
    at div
    at div
    at Spin (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:44476:20)
    at SpinFC (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:44545:16)
    at div
    at InternalTable (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:61444:16)
    at Table2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:61764:35)
    at DataTable (http://localhost:8910/components/DataTable/DataTable.tsx:102:3)
    at div
    at div
    at DrawerPanel3 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:38201:5)
    at div
    at DrawerPanel2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:37892:25)
    at div
    at DomWrapper2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-D3WACKKS.js?v=9f539c35:3316:5)
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-D3WACKKS.js?v=9f539c35:3729:32
    at div
    at DrawerPopup (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:37939:25)
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-UEK5KTZZ.js?v=9f539c35:285:20
    at Drawer2 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:38122:27)
    at NoFormStyle (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-MV2XQFT2.js?v=9f539c35:4752:5)
    at NoCompactStyle (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-UEK5KTZZ.js?v=9f539c35:2972:5)
    at Drawer3 (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:38585:5)
    at EmissionsFactorSelector (http://localhost:8910/components/EmissionsFactorSelector/EmissionsFactorSelector.tsx:51:3)
    at main
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14443:16
    at Content
    at div
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14462:16
    at Layout
    at AddProduct (http://localhost:8910/components/AddProduct/AddProduct.tsx?t=1748749103752:313:23)
    at main
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14443:16
    at Content
    at div
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14462:16
    at Layout
    at main
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14443:16
    at Content
    at AddProductPage (http://localhost:8910/pages/AddProductPage/AddProductPage.tsx:45:28)
    at Suspense
    at ActiveRouteLoader (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:665:3)
    at AuthenticatedRoute (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:966:3)
    at div
    at main
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14443:16
    at Content
    at div
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14462:16
    at Layout
    at div
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/antd.js?v=9f539c35:14462:16
    at Layout
    at MotionWrapper (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-D3WACKKS.js?v=9f539c35:4004:5)
    at ProviderChildren (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-D3WACKKS.js?v=9f539c35:4106:5)
    at ConfigProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-D3WACKKS.js?v=9f539c35:4355:27)
    at MainLayout (http://localhost:8910/layouts/MainLayout/MainLayout.tsx:40:23)
    at http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:1672:47
    at PageLoadingContextProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:544:3)
    at ParamsProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:572:25)
    at RouterContextProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:937:3)
    at LocationAwareRouter (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:1569:3)
    at LocationProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:204:5)
    at Router (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_router.js?v=9f539c35:1548:3)
    at Routes
    at GraphQLHooksProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-6MZZAMPR.js?v=9f539c35:9817:13)
    at ErrorBoundary (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_web_apollo.js?v=9f539c35:2659:21)
    at ApolloProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-VJD64DIA.js?v=9f539c35:10029:20)
    at ApolloProviderWithFetchConfig (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_web_apollo.js?v=9f539c35:2550:40)
    at FetchConfigProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-6MZZAMPR.js?v=9f539c35:9749:3)
    at RedwoodApolloProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_web_apollo.js?v=9f539c35:2669:3)
    at AuthProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/chunk-OB4A7IRS.js?v=9f539c35:404:27)
    at _a (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_web.js?v=9f539c35:1034:5)
    at RedwoodProvider (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@redwoodjs_web.js?v=9f539c35:1383:3)
    at _ErrorBoundary (http://localhost:8910/@fs/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/node_modules/.vite/deps/@sentry_react.js?v=9f539c35:26782:5)
    at App
=== EMISSIONS FACTOR SEARCH DEBUG START ===
1. About to fill search field with "citric acid"
2. Search field filled, about to click search button
4. Search button clicked, waiting for API response...
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 37389ms
GraphQL Response: {"errors":[{"message":"Source country for '1,4-Dioxane' not found.","locations":[{"line":2,"column":3}],"path":["predictIngredientSource"]}],"data":{"predictIngredientSource":null}}
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 35301ms
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 37059ms
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 39295ms
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 30376ms
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"2-Tridecenenitrile","hscode":"292690","productCategory":"Nitrile-function compounds; n.e.s. in heading no. 2926","manufacturingCountry":"GBR","countryCode":"FRA","country":"France","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"2-Octanone","hscode":"291419","productCategory":"Ketones; acyclic, without other oxygen function, n.e.s. in item no. 2914.1","manufacturingCountry":"GBR","countryCode":"CHN","country":"China","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
GraphQL Response: {"errors":[{"message":"Source country for '2-Bromobutane' not found.","locations":[{"line":2,"column":3}],"path":["predictIngredientSource"]}],"data":{"predictIngredientSource":null}}
  ✘  2 [chromium] › e2e/02-01-inventory-add-product.spec.ts:21:5 › Add Product (2.3m)ted","manufacturingCountry":"GBR","countryCode":"USA","country":"United States","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 39263ms
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"Acetyl Propionyl","hscode":"291419","productCategory":"Ketones; acyclic, without other oxygen function, n.e.s. in item no. 2914.1","manufacturingCountry":"GBR","countryCode":"CHN","country":"China","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 38095ms
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 36263ms
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 39947ms
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"Acrylates Copolymer","hscode":"390690","productCategory":"Acrylic polymers; (other than polymethyl methacrylate), in primary forms","manufacturingCountry":"GBR","countryCode":"GBR","country":"United Kingdom","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"2-Methoxynaphthalene","hscode":"290930","productCategory":"Ethers; aromatic, and their halogenated, sulphonated, nitrated or nitrosated derivatives","manufacturingCountry":"GBR","countryCode":"GBR","country":"United Kingdom","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 42196ms
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"2-Propylheptanol","hscode":"290519","productCategory":"Alcohols; saturated monohydric, n.e.s. in item no. 2905.1","manufacturingCountry":"GBR","countryCode":"DEU","country":"Germany","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"Acrylic Latex","hscode":"320990","productCategory":"Paints and varnishes; (based on polymers other than acrylic or vinyl), dispersed or dissolved in an aqueous medium","manufacturingCountry":"GBR","countryCode":"GBR","country":"United Kingdom","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}
Response URL: http://localhost:8910/.redwood/functions/graphql
Response Status: 200
Response Time: 42163ms
GraphQL Response: {"data":{"predictIngredientSource":{"ingredientName":"2-Methylundecanal","hscode":"291229","productCategory":"Aldehydes; cyclic, without other oxygen function, other than benzaldehyde","manufacturingCountry":"GBR","countryCode":"GBR","country":"United Kingdom","countryCapital":null,"latitude":null,"longitude":null,"locallyProcured":false,"__typename":"IngredientSource"}},"extensions":{}}


  1) [chromium] › e2e/02-01-inventory-add-product.spec.ts:21:5 › Add Product ───────────────────────

    TimeoutError: page.waitForResponse: Timeout 30000ms exceeded while waiting for event "response"

      314 |
      315 |   // Wait for the API response
    > 316 |   await page.waitForResponse(response =>
          |              ^
      317 |     response.url().includes('/raw-materials/activities/recommendations') && response.status() === 200,
      318 |     { timeout: 30000 }
      319 |   );

        at /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/tests/e2e/02-01-inventory-add-product.spec.ts:316:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/e2e-02-01-inventory-add-product-Add-Product-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/e2e-02-01-inventory-add-product-Add-Product-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/e2e-02-01-inventory-add-product-Add-Product-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  Slow test file: [chromium] › e2e/02-01-inventory-add-product.spec.ts (2.3m)
  Consider splitting slow test files to speed up parallel execution
  1 failed
    [chromium] › e2e/02-01-inventory-add-product.spec.ts:21:5 › Add Product ────────────────────────
  1 passed (2.5m)
